// Screen Manager for handling transitions between screens
class ScreenManager {
  constructor() {
    this.currentScreen = null;
    this.screens = {};
    this.transitionDuration = 300;
  }

  registerScreen(name, screenElement) {
    this.screens[name] = screenElement;
  }

  async showScreen(screenName) {
    const screen = this.screens[screenName];
    if (!screen) {
      console.error(`Screen '${screenName}' not found`);
      return;
    }

    // Hide current screen
    if (this.currentScreen && this.screens[this.currentScreen]) {
      await this.hideScreen(this.currentScreen);
    }

    // Show new screen
    screen.style.display = 'block';
    screen.classList.remove('screen-hidden');
    screen.classList.add('screen-visible');
    
    this.currentScreen = screenName;
  }

  async hideScreen(screenName) {
    const screen = this.screens[screenName];
    if (!screen) return;

    screen.classList.remove('screen-visible');
    screen.classList.add('screen-hidden');
    
    // Wait for transition to complete
    await new Promise(resolve => setTimeout(resolve, this.transitionDuration));
    screen.style.display = 'none';
  }

  getCurrentScreen() {
    return this.currentScreen;
  }
}

// Subdomain Configuration Screen Class
class SubdomainConfigScreen {
  constructor(popupManager) {
    this.popupManager = popupManager;
    this.elements = {};
    this.bindElements();
    this.setupEventListeners();
  }

  bindElements() {
    this.elements = {
      // Company selection elements
      findCompanyTabsBtn: document.getElementById('findCompanyTabsBtn'),
      companyTabsContainer: document.getElementById('companyTabsContainer'),
      companySearchContainer: document.getElementById('companySearchContainer'),
      companyTabsList: document.getElementById('companyTabsList'),
      companySearchInput: document.getElementById('companySearchInput'),
      noCompanyTabsMessage: document.getElementById('noCompanyTabsMessage'),
      companyTabCountBadge: document.getElementById('companyTabCountBadge'),
      companyManualEntry: document.getElementById('companyManualEntry'),
      selectedCompanyStatus: document.getElementById('selectedCompanyStatus'),
      // Subdomain save elements
      serverUrl: document.getElementById('serverUrl'),
      saveSubdomainBtn: document.getElementById('saveSubdomainBtn'),
      // Subdomain state elements
      subdomainSelectionState: document.getElementById('subdomainSelectionState'),
      subdomainSavedState: document.getElementById('subdomainSavedState'),
      editSubdomainBtn: document.getElementById('editSubdomainBtn'),
      editSubdomainContainer: document.getElementById('editSubdomainContainer'),
      currentSubdomain: document.getElementById('currentSubdomain'),
      currentUrl: document.getElementById('currentUrl')
    };
  }

  setupEventListeners() {
    // Company selection event listeners
    this.elements.findCompanyTabsBtn.addEventListener('click', () => this.findPromokitCompanyTabs());
    this.elements.companySearchInput.addEventListener('input', (e) => this.filterCompanyTabs(e.target.value.toLowerCase()));
    this.elements.companyManualEntry.addEventListener('click', () => this.enableManualEntry());
    
    // Subdomain save event listeners
    this.elements.saveSubdomainBtn.addEventListener('click', () => this.saveSubdomain());
    
    // Subdomain state event listeners
    if (this.elements.editSubdomainBtn) {
      this.elements.editSubdomainBtn.addEventListener('click', () => this.showSubdomainSelectionState());
    }

    this.elements.serverUrl.addEventListener('input', () => this.validateSubdomainField());
  }

  async initialize() {
    await this.loadSelectedCompany();
    this.initializeSubdomainState();
    // Auto-search companies if no company is saved
    setTimeout(() => {
      this.checkAndSearchCompanies();
    }, 500);
  }

  // Subdomain validation and utility methods
  validateSubdomainField() {
    const input = this.elements.serverUrl.value.trim();
    const validation = this.validateSubdomainInput(input);
    
    // Apply visual styles based on validation
    if (input === '') {
      // Empty field - neutral state
      this.elements.serverUrl.style.borderColor = '#ced4da';
      this.elements.saveSubdomainBtn.disabled = false;
      this.clearSubdomainError();
    } else if (validation.valid) {
      // Valid - green border
      this.elements.serverUrl.style.borderColor = '#25d366';
      this.elements.saveSubdomainBtn.disabled = false;
      this.clearSubdomainError();
    } else {
      // Invalid - red border
      this.elements.serverUrl.style.borderColor = '#dc3545';
      this.elements.saveSubdomainBtn.disabled = true;
      this.showSubdomainError(validation.message);
    }
  }

  showSubdomainError(message) {
    // Remove previous error if exists
    this.clearSubdomainError();
    
    // Create error element
    const errorElement = document.createElement('div');
    errorElement.id = 'subdomainError';
    errorElement.className = 'subdomain-error';
    errorElement.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;
    
    // Insert before input group
    const inputGroup = this.elements.serverUrl.closest('.subdomain-input-group');
    if (inputGroup) {
      inputGroup.parentNode.insertBefore(errorElement, inputGroup);
    }
  }

  clearSubdomainError() {
    const existingError = document.getElementById('subdomainError');
    if (existingError) {
      existingError.remove();
    }
  }

  extractSubdomain(url) {
    try {
      const urlObj = new URL(url);
      const hostname = urlObj.hostname;
      
      // Extract subdomain from format: subdomain.promokit.com.br
      if (hostname.endsWith('.promokit.com.br')) {
        const parts = hostname.split('.');
        return parts[0]; // Return only the first part (subdomain)
      }
      
      return null;
    } catch (e) {
      return null;
    }
  }

  buildUrlFromSubdomain(subdomain) {
    if (!subdomain || subdomain.trim() === '') {
      return null;
    }
    
    // Build default URL: https://[subdomain].promokit.com.br
    return `https://${subdomain.trim()}.promokit.com.br`;
  }

  validateSubdomainInput(input) {
    if (!input || input.trim() === '') {
      return { valid: false, message: 'Subdomínio não pode ser vazio' };
    }
    
    const cleanInput = input.trim();
    
    // Check if user typed a complete URL
    if (cleanInput.includes('://') || cleanInput.includes('.com') || cleanInput.includes('www.') || cleanInput.includes('http')) {
      return { 
        valid: false, 
        message: 'Digite apenas o subdomínio (ex: fibo), não uma URL completa' 
      };
    }
    
    // Check if contains slash or other URL characters
    if (cleanInput.includes('/') || cleanInput.includes('?') || cleanInput.includes('#') || cleanInput.includes('&')) {
      return { 
        valid: false, 
        message: 'Digite apenas o subdomínio, sem barras ou caracteres especiais' 
      };
    }
    
    const subdomain = cleanInput.toLowerCase();
    
    // Check if contains dots (indicative of complete domain)
    if (subdomain.includes('.')) {
      return { 
        valid: false, 
        message: 'Digite apenas o subdomínio (ex: fibo), sem pontos ou domínio' 
      };
    }
    
    // Validate minimum and maximum length
    if (subdomain.length < 2) {
      return { 
        valid: false, 
        message: 'Subdomínio deve ter pelo menos 2 caracteres' 
      };
    }
    
    if (subdomain.length > 63) {
      return { 
        valid: false, 
        message: 'Subdomínio não pode ter mais de 63 caracteres' 
      };
    }
    
    // Validate subdomain format - only letters, numbers and hyphen
    if (!/^[a-z0-9-]+$/.test(subdomain)) {
      return { 
        valid: false, 
        message: 'Subdomínio deve conter apenas letras, números e hífen (sem acentos ou caracteres especiais)' 
      };
    }
    
    // Cannot start or end with hyphen
    if (subdomain.startsWith('-') || subdomain.endsWith('-')) {
      return { 
        valid: false, 
        message: 'Subdomínio não pode começar ou terminar com hífen' 
      };
    }
    
    // Cannot have consecutive hyphens
    if (subdomain.includes('--')) {
      return { 
        valid: false, 
        message: 'Subdomínio não pode ter hífens consecutivos' 
      };
    }
    
    // Cannot be only numbers
    if (/^\d+$/.test(subdomain)) {
      return { 
        valid: false, 
        message: 'Subdomínio não pode ser apenas números' 
      };
    }
    
    return { valid: true, subdomain: subdomain };
  }

  formatPromokitSubdomain(input) {
    try {
      // If it's a complete URL, extract subdomain
      if (input.startsWith('http')) {
        return this.extractSubdomain(input);
      }
      
      // If it's just subdomain, validate format
      const validation = this.validateSubdomainInput(input);
      if (validation.valid) {
        return validation.subdomain;
      }
      
      throw new Error(validation.message);
    } catch (e) {
      throw new Error(`Could not process input: ${e.message}`);
    }
  }

  // Company selection methods
  async loadSelectedCompany() {
    try {
      const result = await chrome.storage.local.get(['selectedCompany']);
      if (result.selectedCompany) {
        const company = result.selectedCompany;
        this.updateSelectedCompanyStatus(company);
        this.popupManager.addLog(`Empresa carregada: ${company.title}`, 'info');
        
        // Auto-configure subdomain if company has one
        let subdomain = null;
        if (company.subdomain) {
          subdomain = company.subdomain;
          this.elements.serverUrl.value = subdomain;
        } else if (company.serverUrl) {
          // Fallback for compatibility with old data
          subdomain = this.extractSubdomain(company.serverUrl);
          if (subdomain) {
            this.elements.serverUrl.value = subdomain;
          }
        }
        
        // Mark selection as complete
        this.markCompanySelectionComplete();
      }
    } catch (error) {
      this.popupManager.addLog(`Erro ao carregar empresa: ${error.message}`, 'error');
    }
  }

  async saveSelectedCompany(companyTab) {
    try {
      const subdomain = this.extractSubdomain(companyTab.url);
      if (!subdomain) {
        throw new Error('Não foi possível extrair subdomínio da URL');
      }

      const companyData = {
        id: companyTab.id,
        subdomain: subdomain,
        title: subdomain, // Use subdomain as title
        url: companyTab.url, // Original tab URL
        hostname: new URL(companyTab.url).hostname,
        favicon: companyTab.favIconUrl,
        selectedAt: new Date().toISOString(),
        serverUrl: this.buildUrlFromSubdomain(subdomain)
      };

      await chrome.storage.local.set({ selectedCompany: companyData });
      this.popupManager.addLog(`Empresa salva: ${subdomain}`, 'success');
      return companyData;
    } catch (error) {
      this.popupManager.addLog(`Erro ao salvar empresa: ${error.message}`, 'error');
      throw error;
    }
  }

  findPromokitCompanyTabs() {
    const btn = this.elements.findCompanyTabsBtn;
    const originalContent = btn.innerHTML;
    
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Buscando...';
    btn.disabled = true;

    // Clear list and show loading
    this.elements.companyTabsList.innerHTML = '';
    this.elements.companyTabsContainer.style.display = 'block';
    this.elements.companySearchContainer.style.display = 'none'; // Hide during loading
    this.elements.noCompanyTabsMessage.style.display = 'flex';
    this.elements.noCompanyTabsMessage.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Buscando empresas PromoKit...';
    this.elements.companyTabCountBadge.style.display = 'none';

    chrome.tabs.query({}, (tabs) => {
      // Filter only PromoKit tabs
      const promokitTabs = tabs.filter(tab => {
        try {
          const url = new URL(tab.url);
          return url.protocol === 'https:' && url.hostname.endsWith('promokit.com.br');
        } catch (e) {
          return false;
        }
      });

      // Small delay for UX
      setTimeout(() => {
        btn.innerHTML = originalContent;
        btn.disabled = false;

        if (promokitTabs.length > 0) {
          this.displayCompanyTabs(promokitTabs);
          this.elements.noCompanyTabsMessage.style.display = 'none';
          this.elements.companyTabCountBadge.textContent = promokitTabs.length;
          this.elements.companyTabCountBadge.style.display = 'inline-flex';

          // Show filter only if there's more than 1 company
          if (promokitTabs.length > 1) {
            this.elements.companySearchContainer.style.display = 'block';
          } else {
            this.elements.companySearchContainer.style.display = 'none';
            // Clear search field when hidden
            this.elements.companySearchInput.value = '';
          }

          // Auto-select first company if none is selected
          this.autoSelectFirstCompany(promokitTabs);
        } else {
          this.elements.noCompanyTabsMessage.style.display = 'flex';
          this.elements.noCompanyTabsMessage.innerHTML = '<i class="fas fa-exclamation-circle"></i> Nenhuma empresa PromoKit encontrada.';
          this.elements.companyTabCountBadge.style.display = 'none';
          // Hide filter when no companies
          this.elements.companySearchContainer.style.display = 'none';
          this.elements.companySearchInput.value = '';
        }
      }, 800);
    });
  }

  displayCompanyTabs(tabs) {
    this.elements.companyTabsList.innerHTML = '';
    
    tabs.forEach((tab, index) => {
      const subdomain = this.extractSubdomain(tab.url);
      if (!subdomain) {
        console.warn('Could not extract subdomain from:', tab.url);
        return; // Skip if cannot extract subdomain
      }

      const tabItem = document.createElement('div');
      tabItem.className = 'company-tab-item';

      // Truncate URL for display
      const displayUrl = tab.url.length > 50 ? 
        tab.url.substring(0, 47) + '...' : 
        tab.url;

      tabItem.innerHTML = `
        <div class="company-tab-content">
          <div class="company-tab-title">${index + 1}. ${subdomain}</div>
          <div class="company-tab-url" title="${tab.url}">${displayUrl}</div>
        </div>
        <button class="company-select-btn">Selecionar</button>
      `;

      // Event listeners
      const selectTab = () => this.selectCompanyTab(tab, tabItem);
      tabItem.addEventListener('click', selectTab);
      tabItem.querySelector('.company-select-btn').addEventListener('click', (e) => {
        e.stopPropagation();
        selectTab();
      });

      this.elements.companyTabsList.appendChild(tabItem);
    });
  }

  async selectCompanyTab(tab, tabElement) {
    try {
      // Update visual
      document.querySelectorAll('.company-tab-item').forEach(item => {
        item.classList.remove('selected');
        const btn = item.querySelector('.company-select-btn');
        btn.textContent = 'Selecionar';
        btn.classList.remove('selected');
      });

      tabElement.classList.add('selected');
      const selectBtn = tabElement.querySelector('.company-select-btn');
      selectBtn.textContent = 'Selecionada';
      selectBtn.classList.add('selected');

      // Save company
      const companyData = await this.saveSelectedCompany(tab);
      
      // Update subdomain field
      this.elements.serverUrl.value = companyData.subdomain;
      
      // Update status
      this.updateSelectedCompanyStatus(companyData);
      
      // Mark selection as complete and show saved state
      this.markCompanySelectionComplete();
      setTimeout(() => {
        this.showSubdomainSavedState(companyData);
        // Notify popup manager that configuration is complete
        this.popupManager.onSubdomainConfigured(companyData);
      }, 600); // Delay to allow completion animation
      
      this.popupManager.addLog(`Empresa selecionada: ${tab.title}`, 'success');
    } catch (error) {
      this.popupManager.addLog(`Erro ao selecionar empresa: ${error.message}`, 'error');
    }
  }

  async autoSelectFirstCompany(tabs) {
    try {
      const result = await chrome.storage.local.get(['selectedCompany']);
      if (!result.selectedCompany && tabs.length > 0) {
        const firstTab = tabs[0];
        const firstTabElement = this.elements.companyTabsList.querySelector('.company-tab-item');
        if (firstTabElement) {
          await this.selectCompanyTab(firstTab, firstTabElement);
        }
      }
    } catch (error) {
      console.error('Error auto-selecting first company:', error);
    }
  }

  filterCompanyTabs(searchTerm) {
    const tabItems = document.querySelectorAll('.company-tab-item');
    let visibleCount = 0;

    tabItems.forEach(item => {
      const title = item.querySelector('.company-tab-title').textContent.toLowerCase();
      const url = item.querySelector('.company-tab-url').textContent.toLowerCase();

      if (title.includes(searchTerm) || url.includes(searchTerm)) {
        item.style.display = 'flex';
        visibleCount++;
      } else {
        item.style.display = 'none';
      }
    });

    // Show message if no results
    if (visibleCount === 0 && tabItems.length > 0) {
      this.elements.noCompanyTabsMessage.style.display = 'flex';
      this.elements.noCompanyTabsMessage.innerHTML = `<i class="fas fa-search"></i> Nenhuma empresa encontrada para "${searchTerm}"`;
    } else {
      this.elements.noCompanyTabsMessage.style.display = 'none';
    }
  }

  updateSelectedCompanyStatus(companyData) {
    if (companyData) {
      this.elements.selectedCompanyStatus.style.display = 'flex';
      this.elements.selectedCompanyStatus.className = 'company-status-indicator selected';
      
      // Show subdomain and complete domain
      const displayText = companyData.subdomain ? 
        `${companyData.subdomain} (${companyData.subdomain}.promokit.com.br)` : 
        companyData.title;
        
      this.elements.selectedCompanyStatus.innerHTML = `
        <i class="fas fa-check-circle"></i>
        <span>Empresa selecionada: ${displayText}</span>
      `;
    } else {
      this.elements.selectedCompanyStatus.style.display = 'flex';
      this.elements.selectedCompanyStatus.className = 'company-status-indicator';
      this.elements.selectedCompanyStatus.innerHTML = `
        <i class="fas fa-info-circle"></i>
        <span>Nenhuma empresa selecionada</span>
      `;
    }
  }

  enableManualEntry() {
    // Mark company selection complete for manual entry
    this.markCompanySelectionComplete();
    
    // Focus on subdomain field after showing content
    setTimeout(() => {
      this.elements.serverUrl.disabled = false;
      this.elements.serverUrl.focus();
      this.elements.serverUrl.placeholder = 'subdominio';
      this.popupManager.addLog('Entrada manual habilitada - Digite apenas o subdomínio', 'info');
      // Notify popup manager that manual entry is enabled
      this.popupManager.onManualEntryEnabled();
    }, 300);
  }

  markCompanySelectionComplete() {
    const companySection = document.getElementById('companySelectionSection');
    if (companySection) {
      companySection.classList.add('completed');
      
      // Add completion indicator
      const stepContainer = companySection.querySelector('.step-container');
      if (stepContainer) {
        stepContainer.classList.add('step-completed');
      }
      
      // Add success message if it doesn't exist
      let successMessage = companySection.querySelector('.success-message');
      if (!successMessage) {
        successMessage = document.createElement('div');
        successMessage.className = 'success-message';
        successMessage.innerHTML = `
          <i class="fas fa-check-circle"></i>
          <span>Empresa configurada! Agora você pode conectar ao servidor.</span>
        `;
        companySection.appendChild(successMessage);
      }
    }
  }

  // Subdomain management methods
  async saveSubdomain() {
    try {
      const subdomainInput = this.elements.serverUrl.value.trim();
      
      if (!subdomainInput) {
        this.popupManager.addLog('Digite um subdomínio', 'error');
        return;
      }

      // Validate subdomain
      const validation = this.validateSubdomainInput(subdomainInput);
      if (!validation.valid) {
        this.popupManager.addLog(`Erro: ${validation.message}`, 'error');
        return;
      }

      // Save subdomain
      const companyData = {
        subdomain: validation.subdomain,
        title: validation.subdomain,
        url: this.buildUrlFromSubdomain(validation.subdomain),
        serverUrl: this.buildUrlFromSubdomain(validation.subdomain),
        selectedAt: new Date().toISOString()
      };

      await chrome.storage.local.set({ selectedCompany: companyData });
      
      // Clear any validation errors and reset field state
      this.clearSubdomainError();
      this.elements.serverUrl.style.borderColor = '#25d366';
      
      // Update interface using new state system
      this.showSubdomainSavedState(companyData);
      this.popupManager.addLog(`Subdomínio salvo: ${validation.subdomain}`, 'success');
      
      // Update company status
      this.updateSelectedCompanyStatus(companyData);
      
      // Notify popup manager that configuration is complete
      this.popupManager.onSubdomainConfigured(companyData);
      
    } catch (error) {
      this.popupManager.addLog(`Erro ao salvar: ${error.message}`, 'error');
    }
  }

  // Subdomain state management methods
  showSubdomainSelectionState() {
    // Show selection state
    this.elements.subdomainSelectionState.style.display = 'block';
    this.elements.subdomainSavedState.style.display = 'none';
    this.elements.editSubdomainContainer.style.display = 'none';
    
    // Clear manual entry fields
    this.elements.serverUrl.value = '';
    this.elements.serverUrl.disabled = false;
    this.elements.saveSubdomainBtn.disabled = false;
    this.elements.saveSubdomainBtn.innerHTML = '<i class="fas fa-save"></i> Salvar Subdomínio';
    this.elements.saveSubdomainBtn.classList.remove('btn-disabled');
    
    this.popupManager.addLog('Modo de seleção de empresa ativado', 'info');
    
    // Notify popup manager to show config screen
    this.popupManager.onSubdomainEditRequested();
  }

  showSubdomainSavedState(subdomainData) {
    // Hide selection state
    this.elements.subdomainSelectionState.style.display = 'none';
    this.elements.subdomainSavedState.style.display = 'block';
    this.elements.editSubdomainContainer.style.display = 'block';
    
    // Update saved company information
    if (subdomainData) {
      this.elements.currentSubdomain.textContent = subdomainData.subdomain;
      this.elements.currentUrl.textContent = subdomainData.serverUrl || this.buildUrlFromSubdomain(subdomainData.subdomain);
    }
    
    this.popupManager.addLog(`Empresa configurada: ${subdomainData?.subdomain || 'empresa'}`, 'success');
  }

  initializeSubdomainState() {
    chrome.storage.local.get(['selectedCompany']).then(result => {
      if (result.selectedCompany && result.selectedCompany.subdomain) {
        // Has saved company - show saved state
        this.showSubdomainSavedState(result.selectedCompany);
        // Notify popup manager that config is complete
        this.popupManager.onSubdomainConfigured(result.selectedCompany);
      } else {
        // No saved company - show selection state
        this.showSubdomainSelectionState();
      }
    }).catch(error => {
      console.error('Error initializing subdomain state:', error);
      // Fallback to selection state
      this.showSubdomainSelectionState();
    });
  }

  async checkAndSearchCompanies() {
    try {
      const result = await chrome.storage.local.get(['selectedCompany']);
      
      // Only search companies if no company is saved
      if (!result.selectedCompany || !result.selectedCompany.subdomain) {
        this.findPromokitCompanyTabs();
      }
    } catch (error) {
      console.error('Error checking companies:', error);
      // Search companies as fallback
      this.findPromokitCompanyTabs();
    }
  }
}

// WebSocket Connection Screen Class
class WebSocketConnectionScreen {
  constructor(popupManager) {
    this.popupManager = popupManager;
    this.elements = {};
    this.bindElements();
    this.setupEventListeners();
  }

  bindElements() {
    this.elements = {
      autoConnect: document.getElementById('autoConnect'),
      connectBtn: document.getElementById('connectBtn'),
      disconnectBtn: document.getElementById('disconnectBtn'),
      saveConfigBtn: document.getElementById('saveConfigBtn'),
      reconnectAttempts: document.getElementById('reconnectAttempts'),
      lastConnection: document.getElementById('lastConnection'),
      logsContainer: document.getElementById('logsContainer'),
      clearLogsBtn: document.getElementById('clearLogsBtn'),
      testMessage: document.getElementById('testMessage'),
      sendTestBtn: document.getElementById('sendTestBtn'),
      getContactsBtn: document.getElementById('getContactsBtn'),
      pingServerBtn: document.getElementById('pingServerBtn'),
      helpLink: document.getElementById('helpLink'),
      aboutLink: document.getElementById('aboutLink'),
      openDashboardBtn: document.getElementById('openDashboardBtn')
    };
  }

  setupEventListeners() {
    this.elements.connectBtn.addEventListener('click', () => this.connect());
    this.elements.disconnectBtn.addEventListener('click', () => this.disconnect());
    this.elements.saveConfigBtn.addEventListener('click', () => this.saveConfiguration());
    this.elements.clearLogsBtn.addEventListener('click', () => this.popupManager.clearLogs());
    this.elements.sendTestBtn.addEventListener('click', () => this.sendTestMessage());
    this.elements.getContactsBtn.addEventListener('click', () => this.getContacts());
    this.elements.pingServerBtn.addEventListener('click', () => this.pingServer());
    this.elements.helpLink.addEventListener('click', (e) => this.showHelp(e));
    this.elements.aboutLink.addEventListener('click', (e) => this.showAbout(e));
    this.elements.openDashboardBtn.addEventListener('click', () => this.openDashboard());

    this.elements.testMessage.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') this.sendTestMessage();
    });

    // Update status periodically
    setInterval(() => this.updateStatus(), 2000);
  }

  async initialize() {
    await this.loadConfiguration();
    this.updateStatus();
  }

  async loadConfiguration() {
    try {
      const result = await chrome.storage.sync.get(['serverUrl', 'autoConnect', 'lastConnection']);
      
      this.elements.autoConnect.checked = result.autoConnect !== false;
      
      if (result.lastConnection) {
        this.elements.lastConnection.textContent = new Date(result.lastConnection).toLocaleString();
      }
      
      this.popupManager.addLog('Configuração carregada', 'success');
    } catch (error) {
      this.popupManager.addLog(`Erro ao carregar configuração: ${error.message}`, 'error');
    }
  }

  async saveConfiguration() {
    try {
      // Get subdomain from saved company data
      const result = await chrome.storage.local.get(['selectedCompany']);
      
      if (!result.selectedCompany || !result.selectedCompany.subdomain) {
        this.popupManager.addLog('Configure o subdomínio primeiro', 'error');
        return;
      }

      // Build URL from saved subdomain
      const serverUrl = result.selectedCompany.serverUrl;
      
      const config = {
        serverUrl: serverUrl,
        autoConnect: this.elements.autoConnect.checked
      };

      await chrome.storage.sync.set(config);
      await this.popupManager.sendMessage('updateConfig', { config });
      
      this.popupManager.addLog(`Configuração salva: ${result.selectedCompany.subdomain}`, 'success');
      this.elements.saveConfigBtn.textContent = 'Salvo!';
      setTimeout(() => {
        this.elements.saveConfigBtn.textContent = 'Salvar Configuração';
      }, 2000);
    } catch (error) {
      this.popupManager.addLog(`Erro ao salvar configuração: ${error.message}`, 'error');
    }
  }

  async connect() {
    try {
      // Check if subdomain has been saved
      const result = await chrome.storage.local.get(['selectedCompany']);
      
      if (!result.selectedCompany || !result.selectedCompany.subdomain) {
        this.popupManager.addLog('Configure o subdomínio antes de conectar', 'error');
        return;
      }

      // Build configuration with URL based on saved subdomain
      const serverUrl = result.selectedCompany.serverUrl;
      const config = {
        serverUrl: serverUrl,
        autoConnect: this.elements.autoConnect.checked
      };

      // Save configuration
      await chrome.storage.sync.set(config);
      await this.popupManager.sendMessage('updateConfig', { config });

      this.elements.connectBtn.disabled = true;
      this.elements.connectBtn.textContent = 'Conectando...';
      
      this.popupManager.addLog(`Conectando em: ${serverUrl}`, 'info');
      await this.popupManager.sendMessage('connect');
      
      setTimeout(() => {
        this.updateStatus();
      }, 1000);
    } catch (error) {
      this.popupManager.addLog(`Erro ao conectar: ${error.message}`, 'error');
      this.elements.connectBtn.disabled = false;
      this.elements.connectBtn.textContent = 'Conectar';
    }
  }

  async disconnect() {
    try {
      await this.popupManager.sendMessage('disconnect');
    } catch (error) {
      this.popupManager.addLog(`Erro ao desconectar: ${error.message}`, 'error');
    }
  }

  async updateStatus() {
    try {
      const response = await this.popupManager.sendMessage('getStatus');
      
      if (response) {
        this.popupManager.updateStatusIndicator(response.isConnected);
        this.elements.reconnectAttempts.textContent = response.reconnectAttempts || 0;
        
        this.elements.connectBtn.disabled = response.isConnected;
        this.elements.disconnectBtn.disabled = !response.isConnected;
        this.elements.connectBtn.textContent = 'Conectar';
        
        if (response.isConnected) {
          await chrome.storage.sync.set({ lastConnection: new Date().toISOString() });
          this.elements.lastConnection.textContent = new Date().toLocaleString();
        }
      }
    } catch (error) {
      console.error('Error updating status:', error);
    }
  }

  sendTestMessage() {
    const message = this.elements.testMessage.value.trim();
    if (!message) {
      this.popupManager.addLog('Digite uma mensagem de teste', 'warning');
      return;
    }

    const testData = {
      type: 'test_message',
      data: { message, timestamp: new Date().toISOString() }
    };
    
    this.popupManager.sendMessage('sendMessage', { data: testData });
    this.elements.testMessage.value = '';
  }

  getContacts() {
    const contactsData = {
      type: 'get_whatsapp_contacts',
      timestamp: new Date().toISOString()
    };
    
    this.popupManager.sendMessage('sendMessage', { data: contactsData });
  }

  pingServer() {
    const pingData = {
      type: 'ping',
      timestamp: new Date().toISOString()
    };
    
    this.popupManager.sendMessage('sendMessage', { data: pingData });
  }

  openDashboard() {
    // Open dashboard in new tab
    chrome.tabs.create({
      url: chrome.runtime.getURL('dashboard.html')
    });
  }

  showHelp(e) {
    e.preventDefault();
    this.popupManager.addLog('Ajuda: A conexão WebSocket é automática ao abrir o WhatsApp Web', 'info');
    
    const helpText = `
Como usar:
1. Configure a URL do servidor WebSocket
2. Abra o WhatsApp Web em uma aba
3. A conexão WebSocket é estabelecida automaticamente
4. Use as ações de teste para verificar funcionamento
5. A conexão é encerrada ao fechar o WhatsApp Web
6. Monitore os logs para acompanhar eventos
    `.trim();
    
    alert(helpText);
  }

  showAbout(e) {
    e.preventDefault();
    const aboutText = `
Promokit WhatsApp WebSocket v1.0.0

Esta extensão conecta automaticamente ao servidor
WebSocket quando o WhatsApp Web é aberto, permitindo
comunicação em tempo real para automação de mensagens.

A conexão é estabelecida apenas quando necessário
e encerrada ao fechar o WhatsApp Web.

Desenvolvido para integração com o sistema Promokit.
    `.trim();
    
    alert(aboutText);
  }
}

// Main Popup Manager Class - Refactored to coordinate between screens
class PopupManager {
  constructor() {
    this.elements = {};
    this.logs = [];
    this.maxLogs = 50;
    
    // Initialize screen management
    this.screenManager = new ScreenManager();
    this.subdomainConfigScreen = new SubdomainConfigScreen(this);
    this.connectionScreen = new WebSocketConnectionScreen(this);
    
    this.init();
  }

  init() {
    this.bindElements();
    this.setupEventListeners();
    this.setupMessageListener();
    
    // Initialize screens
    this.initializeScreens();
    
    this.addLog('Popup inicializado', 'info');
    
    // Initialize subdomain config screen
    setTimeout(() => {
      this.subdomainConfigScreen.initialize();
    }, 500);
  }

  initializeScreens() {
    // Setup screen containers
    const configScreen = document.getElementById('companySelectionSection');
    const connectionScreen = document.getElementById('mainContent');
    
    // Register screens with screen manager
    this.screenManager.registerScreen('config', configScreen);
    this.screenManager.registerScreen('connection', connectionScreen);
    
    // Start with config screen
    this.screenManager.showScreen('config');
  }

  // Callback methods for screen coordination
  onSubdomainConfigured(companyData) {
    // Initialize and show connection screen when subdomain is configured
    this.connectionScreen.initialize();
    this.screenManager.showScreen('connection');
  }

  onSubdomainEditRequested() {
    // Show config screen when edit is requested
    this.screenManager.showScreen('config');
  }

  onManualEntryEnabled() {
    // Show connection screen for manual entry
    this.connectionScreen.initialize();
    this.screenManager.showScreen('connection');
  }

  bindElements() {
    this.elements = {
      statusIndicator: document.getElementById('statusIndicator'),
      statusDot: document.getElementById('statusDot'),
      statusText: document.getElementById('statusText')
    };
  }

  setupEventListeners() {
    // Main popup manager only handles shared functionality
    // Individual screens handle their own event listeners
  }

  setupMessageListener() {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      switch (message.type) {
        case 'connected':
          if (this.connectionScreen) {
            this.connectionScreen.updateStatus();
          }
          break;
        case 'disconnected':
          if (this.connectionScreen) {
            this.connectionScreen.updateStatus();
          }
          break;
        case 'log':
          if (message.data) {
            this.addLog(message.data.message, message.data.type);
          }
          break;
      }
      sendResponse({ received: true });
    });
  }

  // Configuration loading is now handled by individual screens

  // Configuration saving is now handled by connection screen

  // Connection management is now handled by connection screen

  // Disconnect is now handled by connection screen

  // Status updates are now handled by connection screen

  updateStatusIndicator(isConnected) {
    const dot = this.elements.statusDot;
    const text = this.elements.statusText;
    
    dot.className = 'status-dot';
    
    if (isConnected) {
      dot.classList.add('connected');
      text.textContent = 'Conectado';
    } else {
      dot.classList.add('disconnected');
      text.textContent = 'Desconectado';
    }
  }

  // Test message sending is now handled by connection screen

  // Get contacts is now handled by connection screen

  // Ping server is now handled by connection screen

  sendMessage(action, data = {}) {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage({ action, ...data }, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else if (response && response.error) {
          reject(new Error(response.error));
        } else {
          resolve(response);
        }
      });
    });
  }

  addLog(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = {
      message,
      type,
      timestamp,
      id: Date.now()
    };

    this.logs.unshift(logEntry);
    
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(0, this.maxLogs);
    }

    this.renderLogs();
  }

  renderLogs() {
    const container = document.getElementById('logsContainer');
    if (!container) return;
    
    if (this.logs.length === 0) {
      container.innerHTML = '<div class="log-entry">Nenhum evento registrado</div>';
      return;
    }

    container.innerHTML = this.logs
      .map(log => `
        <div class="log-entry ${log.type}">
          [${log.timestamp}] ${log.message}
        </div>
      `)
      .join('');
    
    container.scrollTop = 0;
  }

  clearLogs() {
    this.logs = [];
    this.renderLogs();
    this.addLog('Logs limpos', 'info');
  }

  // All other methods are now handled by individual screen classes

}

document.addEventListener('DOMContentLoaded', () => {
  new PopupManager();
});
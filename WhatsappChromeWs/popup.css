* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    background: #f5f5f5;
}

.container {
    width: 380px;
    min-height: 500px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.header {
    background: linear-gradient(135deg, #25d366, #128c7e);
    color: white;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo {
    width: 32px;
    height: 32px;
    border-radius: 6px;
}

.header h1 {
    font-size: 18px;
    font-weight: 600;
}

.status-section {
    padding: 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 12px;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #6c757d;
    animation: pulse 2s infinite;
}

.status-dot.connected {
    background: #28a745;
}

.status-dot.disconnected {
    background: #dc3545;
}

.status-dot.connecting {
    background: #ffc107;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.config-section,
.controls-section,
.info-section,
.logs-section,
.actions-section {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #495057;
}

.input-field {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.input-field:focus {
    outline: none;
    border-color: #25d366;
    box-shadow: 0 0 0 2px rgba(37, 211, 102, 0.25);
}

/* Placeholder styling for better UX */
.input-field::placeholder {
    color: #9ca3af;
    opacity: 1;
}

.input-field::-webkit-input-placeholder {
    color: #9ca3af;
    opacity: 1;
}

.input-field::-moz-placeholder {
    color: #9ca3af;
    opacity: 1;
}

.input-field:-ms-input-placeholder {
    color: #9ca3af;
    opacity: 1;
}

.checkbox-label {
    display: flex !important;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    margin-bottom: 0 !important;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
}

.checkmark {
    font-size: 14px;
}

.controls-section {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: #25d366;
    color: white;
}

.btn-primary:hover {
    background: #1fb954;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
}

.btn-outline {
    background: transparent;
    color: #25d366;
    border: 1px solid #25d366;
}

.btn-outline:hover {
    background: #25d366;
    color: white;
}

.btn-small {
    padding: 4px 8px;
    font-size: 12px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.info-section {
    background: #f8f9fa;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-label {
    font-weight: 500;
    color: #495057;
}

.logs-section {
    max-height: 200px;
}

.logs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.logs-header h3 {
    font-size: 16px;
    color: #495057;
}

.logs-container {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 10px;
    max-height: 120px;
    overflow-y: auto;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    font-size: 12px;
}

.log-entry {
    margin-bottom: 4px;
    padding: 2px 4px;
    border-radius: 2px;
}

.log-entry:last-child {
    margin-bottom: 0;
}

.log-entry.success {
    background: #d4edda;
    color: #155724;
}

.log-entry.error {
    background: #f8d7da;
    color: #721c24;
}

.log-entry.warning {
    background: #fff3cd;
    color: #856404;
}

.log-entry.info {
    background: #d1ecf1;
    color: #0c5460;
}

.actions-section h3 {
    font-size: 16px;
    color: #495057;
    margin-bottom: 15px;
}

.test-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    align-items: center;
}

.test-controls:last-child {
    margin-bottom: 0;
}

.test-controls .input-field {
    flex: 1;
}

.footer {
    padding: 15px 20px;
    background: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #6c757d;
}

.version {
    font-weight: 500;
}

.links {
    display: flex;
    gap: 15px;
}

.links a {
    color: #25d366;
    text-decoration: none;
}

.links a:hover {
    text-decoration: underline;
}

.logs-container::-webkit-scrollbar {
    width: 6px;
}

.logs-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.logs-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.logs-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Company Selection Section */
.company-selection-section {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
}

.section-header {
    margin-bottom: 16px;
}

.section-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #25d366;
    margin-bottom: 4px;
}

.section-subtitle {
    font-size: 12px;
    color: #6c757d;
    margin: 0;
}

.step-container {
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.step-number {
    background-color: #25d366;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 12px;
    flex-shrink: 0;
}

.step-content {
    flex: 1;
}

.step-title {
    font-weight: 500;
    font-size: 14px;
    color: #374151;
    margin-bottom: 4px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.step-description {
    font-size: 12px;
    color: #6b7280;
    margin-bottom: 12px;
}

.tab-count-badge {
    background-color: #e0f2fe;
    color: #0284c7;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 10px;
    font-weight: 500;
}

.company-search-btn {
    width: 100%;
    margin-bottom: 12px;
    gap: 8px;
}

.company-tabs-container {
    margin-top: 12px;
}

.company-search-container {
    position: relative;
    margin-bottom: 12px;
}

.company-search-input {
    width: 100%;
    padding: 8px 12px 8px 32px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 12px;
    color: #1f2937;
    transition: all 0.2s;
}

.company-search-input:focus {
    outline: none;
    border-color: #25d366;
    box-shadow: 0 0 0 2px rgba(37, 211, 102, 0.25);
}

.company-search-container .search-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    font-size: 12px;
}

.company-tabs-list {
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 12px;
}

.company-tab-item {
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 10px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.company-tab-item:hover {
    background-color: #f0fdf4;
    border-color: #25d366;
    box-shadow: 0 2px 4px rgba(37, 211, 102, 0.1);
}

.company-tab-item.selected {
    background-color: #dcfce7;
    border-color: #25d366;
    border-width: 2px;
}

.company-tab-content {
    flex: 1;
}

.company-tab-title {
    font-weight: 500;
    font-size: 13px;
    margin-bottom: 2px;
    color: #1f2937;
}

.company-tab-url {
    font-size: 11px;
    color: #6b7280;
    word-break: break-all;
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.company-select-btn {
    background-color: #25d366;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 11px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    white-space: nowrap;
}

.company-select-btn:hover {
    background-color: #1fb954;
}

.company-select-btn.selected {
    background-color: #10b981;
}

.no-tabs-message {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6b7280;
    font-size: 12px;
    background-color: #f9fafb;
    padding: 10px;
    border-radius: 4px;
    border: 1px dashed #d1d5db;
}

.manual-entry-trigger {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #25d366;
    margin-top: 8px;
    cursor: pointer;
    padding: 6px;
    border-radius: 4px;
    transition: all 0.2s;
}

.manual-entry-trigger:hover {
    background-color: #f0fdf4;
    text-decoration: underline;
}

.manual-entry-trigger i {
    margin-right: 4px;
}

.company-status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px;
    background-color: #f9fafb;
    border-radius: 4px;
    margin-top: 8px;
    font-size: 11px;
    color: #6b7280;
    border: 1px dashed #d1d5db;
}

.company-status-indicator.selected {
    background-color: #f0fdf4;
    border: 1px solid #86efac;
    color: #166534;
}

.company-status-indicator i {
    font-size: 12px;
}

/* Dashboard Section */
.dashboard-section {
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
}

.dashboard-controls {
    display: flex;
    justify-content: center;
}

.dashboard-btn {
    width: 100%;
    gap: 8px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    transition: all 0.3s ease;
}

.dashboard-btn:hover {
    background: linear-gradient(135deg, #5a67d8, #6b46c1);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.dashboard-btn i {
    font-size: 14px;
}

/* Main Content Conditional Display */
.main-content {
    opacity: 0;
    transform: translateY(10px);
    transition: opacity 0.4s ease, transform 0.4s ease;
}

.main-content.show {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
}

.main-content.hide {
    opacity: 0;
    transform: translateY(10px);
}

/* Company Selection Completed State */
.company-selection-section.completed {
    background: #f0fdf4;
    border: 1px solid #86efac;
    transition: all 0.3s ease;
}

.company-selection-section.completed .section-header h3 {
    color: #166534;
}

.company-selection-section.completed .step-number {
    background-color: #10b981;
}

/* Progress Indicator */
.progress-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    padding: 12px;
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 6px;
    font-size: 12px;
    color: #0369a1;
}

.progress-indicator.success {
    background: #f0fdf4;
    border: 1px solid #86efac;
    color: #166534;
}

.progress-indicator i {
    color: #0ea5e9;
    font-size: 14px;
}

.progress-indicator.success i {
    color: #10b981;
}

/* Loading States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    z-index: 10;
}

.loading-spinner {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #25d366;
    font-size: 12px;
}

/* Step Completion Animation */
@keyframes checkmark {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.2);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.step-completed .step-number {
    background-color: #10b981;
    animation: checkmark 0.3s ease-out;
}

.step-completed .step-number::after {
    content: '✓';
    position: absolute;
    font-size: 12px;
    font-weight: bold;
}

/* Fade In Animation for Main Content */
@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.main-content.show {
    animation: fadeInUp 0.5s ease-out;
}

/* Disabled State for Company Selection */
.company-selection-section.disabled {
    opacity: 0.6;
    pointer-events: none;
}

/* Success Message */
.success-message {
    background: #f0fdf4;
    border: 1px solid #86efac;
    color: #166534;
    padding: 10px 12px;
    border-radius: 6px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 12px;
}

.success-message i {
    color: #10b981;
    font-size: 14px;
}

/* Subdomain Help Text */
.help-text {
    font-size: 11px;
    color: #6c757d;
    margin-top: 4px;
    font-style: italic;
}

/* Subdomain Input Group */
.subdomain-input-group {
    display: flex;
    gap: 8px;
    align-items: stretch;
}

.subdomain-input-group .input-field {
    flex: 1;
}

.subdomain-save-btn {
    white-space: nowrap;
    padding: 8px 12px;
    font-size: 12px;
    gap: 6px;
    min-width: 140px;
}

.subdomain-save-btn i {
    font-size: 11px;
}

.subdomain-save-btn.btn-disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Subdomain Status */
.subdomain-status {
    background: #f0fdf4;
    border: 1px solid #86efac;
    color: #166534;
    padding: 10px 12px;
    border-radius: 6px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
    transition: all 0.3s ease;
}

.subdomain-status i {
    color: #10b981;
    font-size: 14px;
}

.subdomain-status strong {
    color: #15803d;
    font-weight: 600;
}

.btn-link {
    background: none;
    border: none;
    color: #25d366;
    cursor: pointer;
    text-decoration: underline;
    font-size: 11px;
    margin-left: auto;
    padding: 2px 4px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.btn-link:hover {
    color: #1fb954;
    background: rgba(37, 211, 102, 0.1);
    text-decoration: none;
}

/* Subdomain States */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.section-header-left {
    flex: 1;
}

.section-header-right {
    margin-left: 12px;
}

.section-header-right .btn {
    padding: 6px 12px;
    font-size: 12px;
    gap: 6px;
}

/* Subdomain Saved State */
.subdomain-saved-state {
    display: none;
}

.saved-company-info {
    background: #f0fdf4;
    border: 1px solid #86efac;
    border-radius: 8px;
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 16px;
}

.saved-company-icon {
    flex-shrink: 0;
    width: 48px;
    height: 48px;
    background: #dcfce7;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.saved-company-icon i {
    font-size: 20px;
    color: #16a34a;
}

.saved-company-details {
    flex: 1;
}

.saved-company-details h4 {
    font-size: 14px;
    font-weight: 600;
    color: #15803d;
    margin: 0 0 4px 0;
}

.saved-company-details p {
    font-size: 12px;
    color: #166534;
    margin: 2px 0;
}

.saved-company-details strong {
    font-weight: 600;
    color: #15803d;
}

.url-preview {
    font-style: italic;
    opacity: 0.8;
}

.url-preview span {
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    font-size: 11px;
}

/* Subdomain Mode Switching */
.subdomain-input-mode {
    display: block;
}

.subdomain-display-mode {
    display: none;
}

/* Company Display Format */
.company-display {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 0;
}

.company-label {
    font-weight: 500;
    color: #495057;
    font-size: 14px;
}

.company-value {
    font-weight: 600;
    color: #25d366;
    font-size: 14px;
    background: #f0fdf4;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #d1fae5;
}

.btn-edit-small {
    background: none;
    border: 1px solid #d1d5db;
    color: #6b7280;
    cursor: pointer;
    padding: 3px 8px;
    font-size: 11px;
    border-radius: 4px;
    transition: all 0.2s ease;
    text-decoration: none;
    font-weight: 500;
}

.btn-edit-small:hover {
    background: #f9fafb;
    border-color: #25d366;
    color: #25d366;
}

/* Subdomain Validation Error */
.subdomain-error {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    margin-top: 6px;
    margin-bottom: 8px;
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 4px;
    color: #dc2626;
    font-size: 12px;
    animation: slideDown 0.3s ease-out;
}

.subdomain-error i {
    color: #dc2626;
    font-size: 12px;
}

@keyframes slideDown {
    0% {
        opacity: 0;
        transform: translateY(-10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Screen Transition Styles */
.screen-visible {
    opacity: 1;
    transform: translateX(0);
    transition: all 0.3s ease-in-out;
}

.screen-hidden {
    opacity: 0;
    transform: translateX(-20px);
    transition: all 0.3s ease-in-out;
}

/* Screen containers */
#companySelectionSection,
#mainContent {
    transition: all 0.3s ease-in-out;
}

/* Animation for showing main content */
.main-content {
    transition: all 0.4s ease-in-out;
}

.main-content.show {
    opacity: 1;
    transform: translateY(0);
}

.main-content.hide {
    opacity: 0;
    transform: translateY(10px);
}
}

/* Manual Input Section */
.manual-input-section {
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.manual-input-section .form-group {
    margin-bottom: 0;
}

.manual-input-section label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

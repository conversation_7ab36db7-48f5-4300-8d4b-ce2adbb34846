<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Promokit WebSocket</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <img src="images/icon48.png" alt="Promokit" class="logo">
            <h1>Promokit WebSocket</h1>
        </header>

        <div class="status-section">
            <div class="status-indicator" id="statusIndicator">
                <div class="status-dot" id="statusDot"></div>
                <span id="statusText">Verificando...</span>
            </div>
        </div>

        <!-- Company Selection Section -->
        <div class="company-selection-section" id="companySelectionSection">
            <div class="section-header">
                <div class="section-header-left">
                    <h3>Selecionar Empresa</h3>
                    <p class="section-subtitle">Escolha qual empresa PromoKit conectar</p>
                </div>
                <div class="section-header-right" id="editSubdomainContainer" style="display: none;">
                    <button id="editSubdomainBtn" class="btn btn-outline btn-small">
                        <i class="fas fa-edit"></i>
                        Editar Subdomínio
                    </button>
                </div>
            </div>

            <!-- Estado de Seleção (mostrado quando não há subdomínio) -->
            <div id="subdomainSelectionState" class="subdomain-selection-state">
                <div class="step-container">
                <div class="step-number">1</div>
                <div class="step-content">
                    <div class="step-title">
                        Selecionar empresa 
                        <span id="companyTabCountBadge" class="tab-count-badge" style="display:none">0</span>
                    </div>
                    <div class="step-description">Escolha entre suas páginas abertas do PromoKit ou insira manualmente</div>
                    
                    <button type="button" id="findCompanyTabsBtn" class="btn btn-outline company-search-btn">
                        <i class="fas fa-search"></i>
                        Buscar Empresas PromoKit
                    </button>

                    <div id="companyTabsContainer" class="company-tabs-container" style="display: none;">
                        <div id="companySearchContainer" class="company-search-container">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text" id="companySearchInput" class="company-search-input" placeholder="Filtrar empresas...">
                        </div>

                        <div id="companyTabsList" class="company-tabs-list"></div>

                        <div id="noCompanyTabsMessage" class="no-tabs-message" style="display: none;">
                            <i class="fas fa-exclamation-circle"></i>
                            Nenhuma empresa PromoKit encontrada.
                        </div>

                        <div class="manual-entry-trigger" id="companyManualEntry">
                            <i class="fas fa-edit"></i>
                            Inserir URL manualmente
                        </div>

                        <div id="selectedCompanyStatus" class="company-status-indicator" style="display: none;">
                            <i class="fas fa-info-circle"></i>
                            <span>Nenhuma empresa selecionada</span>
                        </div>
                    </div>
                </div>
            </div>
            </div>
            <!-- End Estado de Seleção -->

            <!-- Estado Salvo (mostrado quando há subdomínio) -->
            <div id="subdomainSavedState" class="subdomain-saved-state" style="display: none;">
                <div class="saved-company-info">
                    <div class="saved-company-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="saved-company-details">
                        <h4>Empresa Configurada</h4>
                        <p>Subdomínio: <strong id="currentSubdomain">-</strong></p>
                        <p class="url-preview">URL: <span id="currentUrl">-</span></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content - Shown only after company selection -->
        <div id="mainContent" class="main-content" style="display: none;">
            <div class="config-section">
            <div class="form-group">
                <!-- Input Mode: Shown when editing -->
                <div id="subdomainInputMode" class="subdomain-input-mode">
                    <label for="serverUrl">Subdomínio da Empresa:</label>
                    <div class="subdomain-input-group">
                        <input type="text" id="serverUrl" placeholder="fibo" class="input-field">
                        <button id="saveSubdomainBtn" class="btn btn-success subdomain-save-btn">
                            <i class="fas fa-save"></i>
                            Salvar Subdomínio
                        </button>
                    </div>
                    <p class="help-text" id="subdomainHelpText">Digite apenas o subdomínio (ex: fibo para https://fibo.promokit.com.br)</p>
                </div>
                
                <!-- Display Mode: Shown when subdomain is saved -->
                <div id="subdomainDisplayMode" class="subdomain-display-mode" style="display: none;">
                    <div class="company-display">
                        <span class="company-label">Empresa:</span>
                        <span class="company-value" id="displaySubdomainText">-</span>
                        <button id="editSubdomainBtn" class="btn-edit-small">editar</button>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="checkbox-label">
                    <input type="checkbox" id="autoConnect">
                    <span class="checkmark"></span>
                    Conectar automaticamente
                </label>
            </div>
        </div>

        <div class="controls-section">
            <button id="connectBtn" class="btn btn-primary">Conectar</button>
            <button id="disconnectBtn" class="btn btn-secondary">Desconectar</button>
            <button id="saveConfigBtn" class="btn btn-success">Salvar Configuração</button>
        </div>

        <div class="info-section">
            <div class="info-item">
                <span class="info-label">Tentativas de Reconexão:</span>
                <span id="reconnectAttempts">0</span>
            </div>
            <div class="info-item">
                <span class="info-label">Última Conexão:</span>
                <span id="lastConnection">Nunca</span>
            </div>
        </div>

        <div class="logs-section">
            <div class="logs-header">
                <h3>Log de Eventos</h3>
                <button id="clearLogsBtn" class="btn btn-small">Limpar</button>
            </div>
            <div class="logs-container" id="logsContainer">
                <div class="log-entry">Aguardando eventos...</div>
            </div>
        </div>

        <div class="actions-section">
            <h3>Ações de Teste</h3>
            <div class="test-controls">
                <input type="text" id="testMessage" placeholder="Mensagem de teste" class="input-field">
                <button id="sendTestBtn" class="btn btn-outline">Enviar Teste</button>
            </div>
            <div class="test-controls">
                <button id="getContactsBtn" class="btn btn-outline">Listar Contatos</button>
                <button id="pingServerBtn" class="btn btn-outline">Ping Servidor</button>
            </div>
        </div>

        <!-- Dashboard Section -->
        <div class="dashboard-section">
            <div class="dashboard-controls">
                <button id="openDashboardBtn" class="btn btn-primary dashboard-btn">
                    <i class="fas fa-chart-bar"></i>
                    Abrir Dashboard
                </button>
            </div>
        </div>

        <footer class="footer">
            <div class="version">v1.0.0</div>
            <div class="links">
                <a href="#" id="helpLink">Ajuda</a>
                <a href="#" id="aboutLink">Sobre</a>
            </div>
        </footer>
        </div>
        <!-- End Main Content -->
    </div>

    <script src="popup.js"></script>
</body>
</html>